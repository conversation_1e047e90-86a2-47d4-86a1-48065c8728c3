import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ArrowDown, Mail, Download, Github, Linkedin } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { useRTL } from "@/hooks/useRTL";

export const Hero = () => {
  const { t } = useTranslation();
  const { iconLeft, isRTL } = useRTL();

  return (
    <section id="home" className="pt-20 min-h-screen flex items-center justify-center bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="mb-8">
          <Avatar className="w-32 h-32 mx-auto mb-6 ring-2 ring-gray-200">
            <AvatarImage src="/lovable-uploads/6d3fd0a4-5be1-42cf-ada4-ae04ab1679d3.png" alt="Waleed Almshwly" />
            <AvatarFallback className="text-xl font-semibold bg-gray-900 text-white">
              WA
            </AvatarFallback>
          </Avatar>
        </div>

        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          {t("fullStackDeveloper")}
        </h1>

        <p className="text-lg md:text-xl text-gray-600 mb-10 max-w-2xl mx-auto">
          {t("heroDescription")}
        </p>

        <div className={`flex flex-col sm:flex-row gap-4 justify-center items-center mb-8 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <Button size="lg" className="bg-gray-900 hover:bg-gray-800 text-white">
            <a href="#projects" className="flex items-center gap-2">
              {t("viewMyWork")}
            </a>
          </Button>
          <Button variant="outline" size="lg">
            <a href="#contact">{t("getInTouch")}</a>
          </Button>
          <Button variant="outline" size="lg">
            <a href="/resume.pdf" download className="flex items-center gap-2">
              <Download className={`w-4 h-4 ${iconLeft}`} />
              {t("downloadCV")}
            </a>
          </Button>
        </div>

        <div className={`flex justify-center gap-6 mb-12 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <a
            href="https://github.com"
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors"
            aria-label="GitHub Profile"
          >
            <Github className="h-5 w-5" />
          </a>
          <a
            href="https://linkedin.com"
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors"
            aria-label="LinkedIn Profile"
          >
            <Linkedin className="h-5 w-5" />
          </a>
          <a
            href="mailto:<EMAIL>"
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-colors"
            aria-label="Send Email"
          >
            <Mail className="h-5 w-5" />
          </a>
        </div>

        <div className="text-center">
          <a href="#about" className="inline-block p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
            <ArrowDown className="h-5 w-5 text-gray-600" />
          </a>
        </div>
      </div>
    </section>
  );
};
