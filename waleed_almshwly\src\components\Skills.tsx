import { useTranslation } from "@/hooks/useTranslation";

export const Skills = () => {
  const { t } = useTranslation();

  const skillCategories = [
    {
      title: t("frontendDevelopment"),
      skills: ["React.js", "Next.js", "TypeScript", "Tailwind CSS", "HTML/CSS", "Vue.js"]
    },
    {
      title: t("backendDevelopment"),
      skills: ["Node.js", "Express.js", "Python", "PostgreSQL", "MongoDB", "REST APIs"]
    },
    {
      title: t("toolsTechnologies"),
      skills: ["Git/GitHub", "Docker", "AWS", "Vercel", "Firebase", "Linux"]
    }
  ];

  return (
    <section id="skills" className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("skills")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("skillsDescription")}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {skillCategories.map((category, index) => (
            <div key={index} className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">{category.title}</h3>
              <div className="space-y-3">
                {category.skills.map((skill, skillIndex) => (
                  <div
                    key={skillIndex}
                    className="bg-gray-50 rounded-lg px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors"
                  >
                    {skill}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
