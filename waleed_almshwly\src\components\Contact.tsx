import { Mail, Phone, MapPin } from "lucide-react";
import { ContactForm } from "@/components/ContactForm";
import { useTranslation } from "@/hooks/useTranslation";

export const Contact = () => {
  const { t } = useTranslation();

  const contactInfo = [
    {
      icon: Mail,
      title: t("email"),
      content: "<EMAIL>",
      href: "mailto:<EMAIL>"
    },
    {
      icon: Phone,
      title: t("phone"),
      content: "+966 XXX XXX XXX",
      href: "tel:+966XXXXXXXXX"
    },
    {
      icon: MapPin,
      title: t("location"),
      content: t("saudiArabia"),
      href: "#"
    }
  ];

  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("contact")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("contactDescription")}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-6">{t("getInTouch")}</h3>
            <div className="space-y-4">
              {contactInfo.map((info, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <info.icon className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{info.title}</div>
                    {info.href !== "#" ? (
                      <a 
                        href={info.href}
                        className="text-gray-600 hover:text-gray-900 transition-colors"
                      >
                        {info.content}
                      </a>
                    ) : (
                      <span className="text-gray-600">{info.content}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Form */}
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-6">{t("sendMessage")}</h3>
            <form className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Input placeholder={t("firstName")} />
                <Input placeholder={t("lastName")} />
              </div>
              <Input type="email" placeholder={t("email")} />
              <Input placeholder={t("subject")} />
              <Textarea placeholder={t("message")} rows={5} />
              <Button className="w-full bg-gray-900 hover:bg-gray-800">
                {t("sendMessage")}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};
