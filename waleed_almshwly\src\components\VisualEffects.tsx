import React, { useEffect, useRef, useState } from 'react';

// Particle System Component
export const ParticleSystem: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'particle';
      
      // Random starting position
      particle.style.left = Math.random() * 100 + '%';
      particle.style.animationDelay = Math.random() * 2 + 's';
      particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
      
      container.appendChild(particle);
      
      // Remove particle after animation
      setTimeout(() => {
        if (container.contains(particle)) {
          container.removeChild(particle);
        }
      }, 10000);
    };

    // Create particles periodically
    const interval = setInterval(createParticle, 800);
    
    // Create initial particles
    for (let i = 0; i < 5; i++) {
      setTimeout(createParticle, i * 200);
    }

    return () => {
      clearInterval(interval);
    };
  }, []);

  return <div ref={containerRef} className="particle-container" />;
};

// Light Trail Effect
export const LightTrail: React.FC = () => {
  const [trails, setTrails] = useState<Array<{ id: number; x: number; y: number }>>([]);

  useEffect(() => {
    let trailId = 0;

    const handleMouseMove = (e: MouseEvent) => {
      const newTrail = {
        id: trailId++,
        x: e.clientX,
        y: e.clientY,
      };

      setTrails(prev => [...prev, newTrail]);

      // Remove trail after animation
      setTimeout(() => {
        setTrails(prev => prev.filter(trail => trail.id !== newTrail.id));
      }, 800);
    };

    // Throttle mouse move events
    let throttleTimer: NodeJS.Timeout | null = null;
    const throttledMouseMove = (e: MouseEvent) => {
      if (throttleTimer) return;
      
      throttleTimer = setTimeout(() => {
        handleMouseMove(e);
        throttleTimer = null;
      }, 50);
    };

    document.addEventListener('mousemove', throttledMouseMove);

    return () => {
      document.removeEventListener('mousemove', throttledMouseMove);
      if (throttleTimer) clearTimeout(throttleTimer);
    };
  }, []);

  return (
    <>
      {trails.map(trail => (
        <div
          key={trail.id}
          className="light-trail"
          style={{
            left: trail.x - 3,
            top: trail.y - 3,
          }}
        />
      ))}
    </>
  );
};

// Ripple Effect Hook
export const useRippleEffect = () => {
  const createRipple = (event: React.MouseEvent<HTMLElement>) => {
    const element = event.currentTarget;
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const ripple = document.createElement('div');
    ripple.className = 'ripple';
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';

    element.appendChild(ripple);

    setTimeout(() => {
      if (element.contains(ripple)) {
        element.removeChild(ripple);
      }
    }, 1200);
  };

  return { createRipple };
};

// Flowing Waves Background
export const FlowingWaves: React.FC = () => {
  return (
    <div className="wave-container">
      <div className="flowing-wave" style={{ animationDelay: '0s' }} />
      <div className="flowing-wave" style={{ animationDelay: '-4s' }} />
      <div className="flowing-wave" style={{ animationDelay: '-8s' }} />
    </div>
  );
};

// Morphing Shapes
export const MorphingShapes: React.FC = () => {
  const shapes = [
    { top: '10%', left: '10%', animationDelay: '0s' },
    { top: '20%', right: '15%', animationDelay: '-3s' },
    { bottom: '15%', left: '20%', animationDelay: '-6s' },
    { bottom: '25%', right: '10%', animationDelay: '-9s' },
  ];

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {shapes.map((shape, index) => (
        <div
          key={index}
          className="morphing-shape"
          style={{
            ...shape,
            animationDelay: shape.animationDelay,
          }}
        />
      ))}
    </div>
  );
};

// Parallax Layers
export const ParallaxLayers: React.FC = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <div className="parallax-layer-1 absolute w-32 h-32 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full opacity-20" 
           style={{ top: '20%', left: '10%' }} />
      <div className="parallax-layer-2 absolute w-24 h-24 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full opacity-20" 
           style={{ top: '60%', right: '15%' }} />
      <div className="parallax-layer-1 absolute w-20 h-20 bg-gradient-to-r from-green-100 to-blue-100 rounded-full opacity-20" 
           style={{ bottom: '30%', left: '20%' }} />
    </div>
  );
};

// Enhanced Button Component with Multiple Effects
interface EnhancedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'liquid';
  className?: string;
}

export const EnhancedButton: React.FC<EnhancedButtonProps> = ({ 
  children, 
  onClick, 
  variant = 'primary',
  className = '' 
}) => {
  const { createRipple } = useRippleEffect();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    createRipple(e);
    onClick?.();
  };

  const baseClasses = "relative overflow-hidden transition-all duration-300 ripple-container";
  const variantClasses = {
    primary: "bg-blue-600 hover:bg-blue-700 text-white magnetic-hover",
    secondary: "bg-gray-200 hover:bg-gray-300 text-gray-800 magnetic-hover",
    liquid: "bg-gradient-to-r from-blue-600 to-purple-600 text-white liquid-button"
  };

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      onClick={handleClick}
    >
      {children}
    </button>
  );
};
