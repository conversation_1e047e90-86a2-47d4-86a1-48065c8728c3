import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export const useContactForm = () => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const submitForm = async (data: ContactFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const resData = await response.json();

      if (!response.ok) throw new Error(resData.error || 'حدث خطأ أثناء الإرسال');

      toast({
        title: 'تم الإرسال بنجاح',
        description: 'سأقوم بالرد عليك في أقرب وقت ممكن.',
      });

      return { success: true };
    } catch (error) {
      console.error(error);
      toast({
        title: 'فشل في الإرسال',
        description: 'حدث خطأ. يرجى المحاولة لاحقاً.',
        variant: 'destructive',
      });
      return { success: false };
    } finally {
      setLoading(false);
    }
  };

  return { submitForm, loading };
};
